/**
 * 情感反馈系统
 * 实现实时情感反馈和用户情感状态监测
 */

import { System } from '../core/System';
import { Entity } from '../core/Entity';
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 反馈类型
 */
export enum FeedbackType {
  EXPLICIT = 'explicit',     // 显式反馈（用户主动给出）
  IMPLICIT = 'implicit',     // 隐式反馈（从行为推断）
  PHYSIOLOGICAL = 'physiological', // 生理反馈（心率、表情等）
  BEHAVIORAL = 'behavioral', // 行为反馈（点击、停留时间等）
}

/**
 * 反馈数据
 */
export interface FeedbackData {
  /** 反馈ID */
  id: string;
  /** 用户ID */
  userId: string;
  /** 反馈类型 */
  type: FeedbackType;
  /** 反馈值 (-1到1，-1表示非常负面，1表示非常正面) */
  value: number;
  /** 置信度 */
  confidence: number;
  /** 反馈来源 */
  source: string;
  /** 相关的情感响应ID */
  relatedResponseId?: string;
  /** 上下文数据 */
  context: any;
  /** 时间戳 */
  timestamp: number;
  /** 反馈描述 */
  description?: string;
}

/**
 * 用户满意度评估结果
 */
export interface UserSatisfactionAssessment {
  /** 用户ID */
  userId: string;
  /** 总体满意度分数 (0-1) */
  overallSatisfaction: number;
  /** 情感响应质量分数 (0-1) */
  emotionResponseQuality: number;
  /** 交互自然度分数 (0-1) */
  interactionNaturalness: number;
  /** 个性化程度分数 (0-1) */
  personalizationLevel: number;
  /** 评估时间戳 */
  timestamp: number;
  /** 评估基于的反馈数量 */
  basedOnFeedbackCount: number;
  /** 置信度 */
  confidence: number;
}

/**
 * 情感响应质量评价
 */
export interface EmotionResponseQualityEvaluation {
  /** 响应ID */
  responseId: string;
  /** 用户ID */
  userId: string;
  /** 准确性分数 (0-1) */
  accuracyScore: number;
  /** 及时性分数 (0-1) */
  timelinessScore: number;
  /** 适当性分数 (0-1) */
  appropriatenessScore: number;
  /** 一致性分数 (0-1) */
  consistencyScore: number;
  /** 总体质量分数 (0-1) */
  overallQuality: number;
  /** 评价时间戳 */
  timestamp: number;
  /** 改进建议 */
  improvementSuggestions: string[];
}

/**
 * 自适应调节参数
 */
export interface AdaptiveAdjustmentParams {
  /** 情感强度调节因子 */
  intensityAdjustment: number;
  /** 响应时间调节因子 */
  responseTimeAdjustment: number;
  /** 表达方式调节 */
  expressionStyleAdjustment: string;
  /** 个性化权重调节 */
  personalizationWeightAdjustment: number;
  /** 调节原因 */
  adjustmentReason: string;
  /** 调节时间戳 */
  timestamp: number;
}

/**
 * 情感反馈系统配置
 */
export interface EmotionFeedbackConfig {
  /** 反馈收集间隔（毫秒） */
  feedbackCollectionInterval: number;
  /** 满意度评估间隔（毫秒） */
  satisfactionAssessmentInterval: number;
  /** 最大反馈历史记录数 */
  maxFeedbackHistory: number;
  /** 自适应调节阈值 */
  adaptiveAdjustmentThreshold: number;
  /** 是否启用实时监控 */
  enableRealTimeMonitoring: boolean;
  /** 是否启用自适应调节 */
  enableAdaptiveAdjustment: boolean;
  /** 是否启用质量评估 */
  enableQualityAssessment: boolean;
  /** 调试模式 */
  debug: boolean;
}

/**
 * 情感反馈系统
 */
export class EmotionFeedbackSystem extends System {
  /** 系统名称 */
  static readonly NAME = 'EmotionFeedbackSystem';

  /** 配置 */
  private config: EmotionFeedbackConfig;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 反馈历史记录 */
  private feedbackHistory: Map<string, FeedbackData[]> = new Map();

  /** 用户满意度历史 */
  private satisfactionHistory: Map<string, UserSatisfactionAssessment[]> = new Map();

  /** 情感响应质量评价历史 */
  private qualityEvaluationHistory: Map<string, EmotionResponseQualityEvaluation[]> = new Map();

  /** 自适应调节历史 */
  private adjustmentHistory: Map<string, AdaptiveAdjustmentParams[]> = new Map();

  /** 实时监控数据 */
  private realtimeMonitoringData: Map<string, any> = new Map();

  /** 最后评估时间 */
  private lastAssessmentTime: number = 0;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: Partial<EmotionFeedbackConfig> = {}) {
    super();

    this.config = {
      feedbackCollectionInterval: 5000, // 5秒
      satisfactionAssessmentInterval: 60000, // 1分钟
      maxFeedbackHistory: 500,
      adaptiveAdjustmentThreshold: 0.3,
      enableRealTimeMonitoring: true,
      enableAdaptiveAdjustment: true,
      enableQualityAssessment: true,
      debug: false,
      ...config,
    };
  }

  /**
   * 初始化系统
   */
  public async initialize(): Promise<void> {
    if (this.initialized) return;

    // 启动定期任务
    this.startPeriodicTasks();

    this.initialized = true;

    if (this.config.debug) {
      console.log('情感反馈系统初始化完成');
    }
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.initialized) return;

    // 实时监控
    if (this.config.enableRealTimeMonitoring) {
      this.performRealtimeMonitoring();
    }

    // 检查是否需要自适应调节
    if (this.config.enableAdaptiveAdjustment) {
      this.checkAdaptiveAdjustment();
    }
  }

  /**
   * 收集反馈
   * @param feedbackData 反馈数据
   */
  public collectFeedback(feedbackData: Omit<FeedbackData, 'id' | 'timestamp'>): void {
    const fullFeedback: FeedbackData = {
      ...feedbackData,
      id: this.generateFeedbackId(),
      timestamp: Date.now(),
    };

    // 获取用户反馈历史
    let userFeedback = this.feedbackHistory.get(feedbackData.userId);
    if (!userFeedback) {
      userFeedback = [];
      this.feedbackHistory.set(feedbackData.userId, userFeedback);
    }

    // 添加反馈
    userFeedback.push(fullFeedback);

    // 限制历史记录数量
    if (userFeedback.length > this.config.maxFeedbackHistory) {
      userFeedback.shift();
    }

    // 触发反馈收集事件
    this.eventEmitter.emit('feedbackCollected', fullFeedback);

    // 如果启用质量评估，立即评估相关响应
    if (this.config.enableQualityAssessment && fullFeedback.relatedResponseId) {
      this.evaluateResponseQuality(fullFeedback);
    }

    if (this.config.debug) {
      console.log('收集反馈:', fullFeedback);
    }
  }

  /**
   * 评估用户满意度
   * @param userId 用户ID
   * @returns 满意度评估结果
   */
  public assessUserSatisfaction(userId: string): UserSatisfactionAssessment | null {
    const userFeedback = this.feedbackHistory.get(userId);
    
    if (!userFeedback || userFeedback.length === 0) {
      return null;
    }

    // 获取最近的反馈（最近1小时）
    const currentTime = Date.now();
    const oneHour = 60 * 60 * 1000;
    const recentFeedback = userFeedback.filter(
      feedback => currentTime - feedback.timestamp <= oneHour
    );

    if (recentFeedback.length === 0) {
      return null;
    }

    // 计算各项分数
    const overallSatisfaction = this.calculateOverallSatisfaction(recentFeedback);
    const emotionResponseQuality = this.calculateEmotionResponseQuality(recentFeedback);
    const interactionNaturalness = this.calculateInteractionNaturalness(recentFeedback);
    const personalizationLevel = this.calculatePersonalizationLevel(recentFeedback);

    // 计算置信度
    const confidence = Math.min(1.0, recentFeedback.length / 10); // 10个反馈达到最高置信度

    const assessment: UserSatisfactionAssessment = {
      userId,
      overallSatisfaction,
      emotionResponseQuality,
      interactionNaturalness,
      personalizationLevel,
      timestamp: currentTime,
      basedOnFeedbackCount: recentFeedback.length,
      confidence,
    };

    // 保存评估结果
    let satisfactionHistory = this.satisfactionHistory.get(userId);
    if (!satisfactionHistory) {
      satisfactionHistory = [];
      this.satisfactionHistory.set(userId, satisfactionHistory);
    }
    satisfactionHistory.push(assessment);

    // 限制历史记录数量
    if (satisfactionHistory.length > 50) {
      satisfactionHistory.shift();
    }

    // 触发满意度评估事件
    this.eventEmitter.emit('satisfactionAssessed', assessment);

    return assessment;
  }

  /**
   * 获取用户满意度历史
   * @param userId 用户ID
   * @param count 获取数量
   * @returns 满意度历史
   */
  public getUserSatisfactionHistory(userId: string, count: number = 10): UserSatisfactionAssessment[] {
    const history = this.satisfactionHistory.get(userId) || [];
    return history.slice(-count);
  }

  /**
   * 获取情感响应质量评价
   * @param responseId 响应ID
   * @returns 质量评价
   */
  public getResponseQualityEvaluation(responseId: string): EmotionResponseQualityEvaluation | null {
    for (const evaluations of this.qualityEvaluationHistory.values()) {
      const evaluation = evaluations.find(evalItem => evalItem.responseId === responseId);
      if (evaluation) {
        return evaluation;
      }
    }
    return null;
  }

  /**
   * 执行自适应调节
   * @param userId 用户ID
   * @param adjustmentParams 调节参数
   */
  public performAdaptiveAdjustment(userId: string, adjustmentParams: AdaptiveAdjustmentParams): void {
    // 获取调节历史
    let adjustmentHistory = this.adjustmentHistory.get(userId);
    if (!adjustmentHistory) {
      adjustmentHistory = [];
      this.adjustmentHistory.set(userId, adjustmentHistory);
    }

    // 添加调节记录
    adjustmentHistory.push(adjustmentParams);

    // 限制历史记录数量
    if (adjustmentHistory.length > 20) {
      adjustmentHistory.shift();
    }

    // 触发自适应调节事件
    this.eventEmitter.emit('adaptiveAdjustmentPerformed', {
      userId,
      adjustmentParams,
    });

    if (this.config.debug) {
      console.log('执行自适应调节:', { userId, adjustmentParams });
    }
  }

  /**
   * 计算总体满意度
   * @param feedback 反馈列表
   * @returns 总体满意度分数
   */
  private calculateOverallSatisfaction(feedback: FeedbackData[]): number {
    if (feedback.length === 0) return 0.5;

    // 计算加权平均值
    let totalWeightedValue = 0;
    let totalWeight = 0;

    for (const fb of feedback) {
      const weight = fb.confidence;
      totalWeightedValue += (fb.value + 1) / 2 * weight; // 将-1到1转换为0到1
      totalWeight += weight;
    }

    return totalWeight > 0 ? totalWeightedValue / totalWeight : 0.5;
  }

  /**
   * 计算情感响应质量
   * @param feedback 反馈列表
   * @returns 情感响应质量分数
   */
  private calculateEmotionResponseQuality(feedback: FeedbackData[]): number {
    // 过滤出与情感响应相关的反馈
    const emotionFeedback = feedback.filter(fb => fb.relatedResponseId);
    
    if (emotionFeedback.length === 0) return 0.5;

    return this.calculateOverallSatisfaction(emotionFeedback);
  }

  /**
   * 计算交互自然度
   * @param feedback 反馈列表
   * @returns 交互自然度分数
   */
  private calculateInteractionNaturalness(feedback: FeedbackData[]): number {
    // 基于隐式反馈计算自然度
    const implicitFeedback = feedback.filter(fb => fb.type === FeedbackType.IMPLICIT);
    
    if (implicitFeedback.length === 0) return 0.5;

    return this.calculateOverallSatisfaction(implicitFeedback);
  }

  /**
   * 计算个性化程度
   * @param feedback 反馈列表
   * @returns 个性化程度分数
   */
  private calculatePersonalizationLevel(feedback: FeedbackData[]): number {
    // 基于用户行为反馈计算个性化程度
    const behavioralFeedback = feedback.filter(fb => fb.type === FeedbackType.BEHAVIORAL);
    
    if (behavioralFeedback.length === 0) return 0.5;

    return this.calculateOverallSatisfaction(behavioralFeedback);
  }

  /**
   * 评估响应质量
   * @param feedback 反馈数据
   */
  private evaluateResponseQuality(feedback: FeedbackData): void {
    if (!feedback.relatedResponseId) return;

    // 计算各项质量分数
    const accuracyScore = this.calculateAccuracyScore(feedback);
    const timelinessScore = this.calculateTimelinessScore(feedback);
    const appropriatenessScore = this.calculateAppropriatenessScore(feedback);
    const consistencyScore = this.calculateConsistencyScore(feedback);

    // 计算总体质量分数
    const overallQuality = (accuracyScore + timelinessScore + appropriatenessScore + consistencyScore) / 4;

    // 生成改进建议
    const improvementSuggestions = this.generateImprovementSuggestions({
      accuracyScore,
      timelinessScore,
      appropriatenessScore,
      consistencyScore,
    });

    const evaluation: EmotionResponseQualityEvaluation = {
      responseId: feedback.relatedResponseId,
      userId: feedback.userId,
      accuracyScore,
      timelinessScore,
      appropriatenessScore,
      consistencyScore,
      overallQuality,
      timestamp: Date.now(),
      improvementSuggestions,
    };

    // 保存评价结果
    let evaluationHistory = this.qualityEvaluationHistory.get(feedback.userId);
    if (!evaluationHistory) {
      evaluationHistory = [];
      this.qualityEvaluationHistory.set(feedback.userId, evaluationHistory);
    }
    evaluationHistory.push(evaluation);

    // 限制历史记录数量
    if (evaluationHistory.length > 100) {
      evaluationHistory.shift();
    }

    // 触发质量评估事件
    this.eventEmitter.emit('responseQualityEvaluated', evaluation);
  }

  /**
   * 计算准确性分数
   * @param feedback 反馈数据
   * @returns 准确性分数
   */
  private calculateAccuracyScore(feedback: FeedbackData): number {
    // 基于反馈值计算准确性
    return (feedback.value + 1) / 2; // 将-1到1转换为0到1
  }

  /**
   * 计算及时性分数
   * @param feedback 反馈数据
   * @returns 及时性分数
   */
  private calculateTimelinessScore(feedback: FeedbackData): number {
    // 简化实现：假设反馈越快，及时性越高
    // 实际应用中应该基于响应时间和反馈时间的差值
    return Math.max(0.1, Math.min(1.0, feedback.confidence));
  }

  /**
   * 计算适当性分数
   * @param feedback 反馈数据
   * @returns 适当性分数
   */
  private calculateAppropriatenessScore(feedback: FeedbackData): number {
    // 基于上下文信息计算适当性
    // 简化实现
    return (feedback.value + 1) / 2;
  }

  /**
   * 计算一致性分数
   * @param feedback 反馈数据
   * @returns 一致性分数
   */
  private calculateConsistencyScore(feedback: FeedbackData): number {
    // 基于历史反馈计算一致性
    const userFeedback = this.feedbackHistory.get(feedback.userId) || [];
    const recentFeedback = userFeedback.slice(-5); // 最近5个反馈
    
    if (recentFeedback.length < 2) return 0.5;

    // 计算方差
    const values = recentFeedback.map(fb => fb.value);
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    
    // 方差越小，一致性越高
    return Math.max(0.1, 1.0 - variance);
  }

  /**
   * 生成改进建议
   * @param scores 各项分数
   * @returns 改进建议列表
   */
  private generateImprovementSuggestions(scores: {
    accuracyScore: number;
    timelinessScore: number;
    appropriatenessScore: number;
    consistencyScore: number;
  }): string[] {
    const suggestions: string[] = [];

    if (scores.accuracyScore < 0.6) {
      suggestions.push('提高情感识别的准确性');
    }

    if (scores.timelinessScore < 0.6) {
      suggestions.push('减少情感响应的延迟时间');
    }

    if (scores.appropriatenessScore < 0.6) {
      suggestions.push('改善情感响应的适当性');
    }

    if (scores.consistencyScore < 0.6) {
      suggestions.push('提高情感响应的一致性');
    }

    return suggestions;
  }

  /**
   * 执行实时监控
   */
  private performRealtimeMonitoring(): void {
    // 监控各用户的实时状态
    for (const [userId, feedbackHistory] of this.feedbackHistory) {
      const recentFeedback = feedbackHistory.filter(
        fb => Date.now() - fb.timestamp <= 30000 // 最近30秒
      );

      if (recentFeedback.length > 0) {
        const avgValue = recentFeedback.reduce((sum, fb) => sum + fb.value, 0) / recentFeedback.length;
        
        // 更新实时监控数据
        this.realtimeMonitoringData.set(userId, {
          recentFeedbackCount: recentFeedback.length,
          averageFeedbackValue: avgValue,
          lastUpdateTime: Date.now(),
        });

        // 检测异常情况
        if (avgValue < -0.5 && recentFeedback.length >= 3) {
          this.eventEmitter.emit('negativeEmotionDetected', {
            userId,
            averageValue: avgValue,
            feedbackCount: recentFeedback.length,
          });
        }
      }
    }
  }

  /**
   * 检查自适应调节
   */
  private checkAdaptiveAdjustment(): void {
    for (const [userId, satisfactionHistory] of this.satisfactionHistory) {
      if (satisfactionHistory.length === 0) continue;

      const latestAssessment = satisfactionHistory[satisfactionHistory.length - 1];
      
      // 如果满意度低于阈值，触发自适应调节
      if (latestAssessment.overallSatisfaction < this.config.adaptiveAdjustmentThreshold) {
        const adjustmentParams: AdaptiveAdjustmentParams = {
          intensityAdjustment: latestAssessment.emotionResponseQuality < 0.5 ? 0.8 : 1.2,
          responseTimeAdjustment: latestAssessment.interactionNaturalness < 0.5 ? 0.8 : 1.0,
          expressionStyleAdjustment: latestAssessment.personalizationLevel < 0.5 ? 'more_expressive' : 'current',
          personalizationWeightAdjustment: latestAssessment.personalizationLevel < 0.5 ? 1.2 : 1.0,
          adjustmentReason: `满意度低于阈值: ${latestAssessment.overallSatisfaction}`,
          timestamp: Date.now(),
        };

        this.performAdaptiveAdjustment(userId, adjustmentParams);
      }
    }
  }

  /**
   * 启动定期任务
   */
  private startPeriodicTasks(): void {
    // 定期评估用户满意度
    setInterval(() => {
      for (const userId of this.feedbackHistory.keys()) {
        this.assessUserSatisfaction(userId);
      }
    }, this.config.satisfactionAssessmentInterval);

    // 定期清理过期数据
    setInterval(() => {
      this.cleanupExpiredData();
    }, 10 * 60 * 1000); // 每10分钟清理一次
  }

  /**
   * 清理过期数据
   */
  private cleanupExpiredData(): void {
    const currentTime = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24小时

    // 清理过期的反馈数据
    for (const [userId, feedbackHistory] of this.feedbackHistory) {
      const validFeedback = feedbackHistory.filter(
        feedback => currentTime - feedback.timestamp <= maxAge
      );
      
      if (validFeedback.length === 0) {
        this.feedbackHistory.delete(userId);
      } else {
        this.feedbackHistory.set(userId, validFeedback);
      }
    }

    // 清理实时监控数据
    for (const [userId, data] of this.realtimeMonitoringData) {
      if (currentTime - data.lastUpdateTime > 5 * 60 * 1000) { // 5分钟无更新
        this.realtimeMonitoringData.delete(userId);
      }
    }

    if (this.config.debug) {
      console.log('清理过期数据完成');
    }
  }

  /**
   * 生成反馈ID
   * @returns 反馈ID
   */
  private generateFeedbackId(): string {
    return `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 销毁系统
   */
  public destroy(): void {
    this.feedbackHistory.clear();
    this.satisfactionHistory.clear();
    this.qualityEvaluationHistory.clear();
    this.adjustmentHistory.clear();
    this.realtimeMonitoringData.clear();
    this.eventEmitter.removeAllListeners();
    this.initialized = false;
  }
}
