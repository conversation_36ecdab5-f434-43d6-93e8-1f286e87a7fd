/**
 * 视觉脚本核心节点
 * 提供基本的流程控制和调试节点
 */
import { EventNode } from '../nodes/EventNode';
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';

/**
 * 开始事件节点
 * 当视觉脚本开始执行时触发
 */
export class OnStartNode extends EventNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '当视觉脚本开始执行时触发'
    });
  }
  
  /**
   * 当视觉脚本开始执行时调用
   */
  public onStart(): void {
    // 触发流程
    this.triggerFlow('flow');
  }
}

/**
 * 更新事件节点
 * 每帧更新时触发
 */
export class OnUpdateNode extends EventNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '每帧更新时触发'
    });
    
    // 添加帧间隔时间输出
    this.addOutput({
      name: 'deltaTime',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '帧间隔时间（秒）'
    });
  }
  
  /**
   * 当视觉脚本更新时调用
   * @param deltaTime 帧间隔时间（秒）
   */
  public onUpdate(deltaTime: number): void {
    // 设置帧间隔时间输出
    this.setOutputValue('deltaTime', deltaTime);
    
    // 触发流程
    this.triggerFlow('flow');
  }
}

/**
 * 分支节点
 * 根据条件选择执行路径
 */
export class BranchNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });
    
    // 添加条件输入
    this.addInput({
      name: 'condition',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '条件',
      defaultValue: false
    });
    
    // 添加输出流程插槽
    this.addOutput({
      name: 'true',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '条件为真时执行'
    });
    
    this.addOutput({
      name: 'false',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '条件为假时执行'
    });
  }
  
  /**
   * 处理输入并确定输出流程
   * @param inputs 输入值
   * @returns 输出流程名称
   */
  protected process(inputs: Record<string, any>): string | null {
    // 获取条件值
    const condition = inputs.condition === true;
    
    // 根据条件选择输出流程
    return condition ? 'true' : 'false';
  }
}

/**
 * 序列节点
 * 按顺序执行多个流程
 */
export class SequenceNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });
    
    // 添加输出流程插槽
    this.addOutput({
      name: 'flow1',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '第一个执行输出'
    });
    
    this.addOutput({
      name: 'flow2',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '第二个执行输出'
    });
    
    this.addOutput({
      name: 'flow3',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '第三个执行输出'
    });
    
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '所有流程执行完成后触发'
    });
  }
  
  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 按顺序触发所有流程
    this.triggerFlow('flow1');
    this.triggerFlow('flow2');
    this.triggerFlow('flow3');
    this.triggerFlow('completed');
    
    return 'completed';
  }
}

/**
 * 打印日志节点
 * 在控制台打印日志
 */
export class PrintLogNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });
    
    // 添加消息输入
    this.addInput({
      name: 'message',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '要打印的消息',
      defaultValue: ''
    });
    
    // 添加日志级别输入
    this.addInput({
      name: 'level',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '日志级别',
      defaultValue: 'log'
    });
    
    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }
  
  /**
   * 处理输入并确定输出流程
   * @param inputs 输入值
   * @returns 输出流程名称
   */
  protected process(inputs: Record<string, any>): string | null {
    // 获取消息和日志级别
    const message = inputs.message || '';
    const level = inputs.level || 'log';
    
    // 打印日志
    switch (level) {
      case 'warn':
        console.warn(`[VisualScript] ${message}`);
        break;
      case 'error':
        console.error(`[VisualScript] ${message}`);
        break;
      case 'info':
        console.info(`[VisualScript] ${message}`);
        break;
      case 'debug':
        console.debug(`[VisualScript] ${message}`);
        break;
      default:
        console.log(`[VisualScript] ${message}`);
        break;
    }
    
    return 'flow';
  }
}

/**
 * 延时节点
 * 延时执行流程
 */
export class DelayNode extends FlowNode {
  /** 定时器ID */
  private timerId: any = null;
  
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });
    
    // 添加延时输入
    this.addInput({
      name: 'seconds',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '延时时间（秒）',
      defaultValue: 1
    });
    
    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '延时后执行'
    });
  }
  
  /**
   * 处理输入并确定输出流程
   * @param inputs 输入值
   * @returns 输出流程名称
   */
  protected process(inputs: Record<string, any>): string | null {
    // 获取延时时间
    const seconds = Math.max(0, inputs.seconds || 0);
    
    // 清除之前的定时器
    if (this.timerId !== null) {
      clearTimeout(this.timerId);
      this.timerId = null;
    }
    
    // 设置定时器
    this.timerId = setTimeout(() => {
      this.triggerFlow('flow');
      this.timerId = null;
    }, seconds * 1000);
    
    // 不立即触发输出流程
    return null;
  }
  
  /**
   * 销毁节点
   */
  public dispose(): void {
    // 清除定时器
    if (this.timerId !== null) {
      clearTimeout(this.timerId);
      this.timerId = null;
    }
    
    super.dispose();
  }
}

/**
 * For循环节点
 * 执行指定次数的循环
 */
export class ForLoopNode extends FlowNode {
  private currentIndex: number = 0;
  private isLooping: boolean = false;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加起始值输入
    this.addInput({
      name: 'start',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '起始值',
      defaultValue: 0
    });

    // 添加结束值输入
    this.addInput({
      name: 'end',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '结束值',
      defaultValue: 10
    });

    // 添加步长输入
    this.addInput({
      name: 'step',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '步长',
      defaultValue: 1
    });

    // 添加循环体输出流程插槽
    this.addOutput({
      name: 'loopBody',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '循环体执行'
    });

    // 添加完成输出流程插槽
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '循环完成后执行'
    });

    // 添加当前索引输出
    this.addOutput({
      name: 'index',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '当前索引'
    });
  }

  /**
   * 处理输入并确定输出流程
   * @param inputs 输入值
   * @returns 输出流程名称
   */
  protected process(inputs: Record<string, any>): string | null {
    if (!this.isLooping) {
      // 开始循环
      const start = inputs.start || 0;
      const end = inputs.end || 10;
      const step = Math.abs(inputs.step || 1);

      this.currentIndex = start;
      this.isLooping = true;

      // 检查循环条件
      if ((step > 0 && this.currentIndex < end) || (step < 0 && this.currentIndex > end)) {
        this.setOutputValue('index', this.currentIndex);
        this.currentIndex += step;
        return 'loopBody';
      } else {
        this.isLooping = false;
        return 'completed';
      }
    }

    return null;
  }

  /**
   * 继续循环执行
   */
  public continueLoop(): void {
    if (this.isLooping) {
      const end = this.getInputValue('end') || 10;
      const step = this.getInputValue('step') || 1;

      if ((step > 0 && this.currentIndex < end) || (step < 0 && this.currentIndex > end)) {
        this.setOutputValue('index', this.currentIndex);
        this.currentIndex += step;
        this.triggerFlow('loopBody');
      } else {
        this.isLooping = false;
        this.triggerFlow('completed');
      }
    }
  }

  /**
   * 重置循环状态
   */
  public resetLoop(): void {
    this.isLooping = false;
    this.currentIndex = 0;
  }
}

/**
 * While循环节点
 * 根据条件执行循环
 */
export class WhileLoopNode extends FlowNode {
  private isLooping: boolean = false;
  private iterationCount: number = 0;
  private maxIterations: number = 1000; // 防止无限循环

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加条件输入
    this.addInput({
      name: 'condition',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '循环条件',
      defaultValue: false
    });

    // 添加最大迭代次数输入
    this.addInput({
      name: 'maxIterations',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '最大迭代次数',
      defaultValue: 1000
    });

    // 添加循环体输出流程插槽
    this.addOutput({
      name: 'loopBody',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '循环体执行'
    });

    // 添加完成输出流程插槽
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '循环完成后执行'
    });

    // 添加迭代次数输出
    this.addOutput({
      name: 'iterations',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '当前迭代次数'
    });
  }

  /**
   * 处理输入并确定输出流程
   * @param inputs 输入值
   * @returns 输出流程名称
   */
  protected process(inputs: Record<string, any>): string | null {
    if (!this.isLooping) {
      // 开始循环
      this.isLooping = true;
      this.iterationCount = 0;
      this.maxIterations = Math.max(1, inputs.maxIterations || 1000);
    }

    // 检查循环条件
    const condition = inputs.condition === true;

    if (condition && this.iterationCount < this.maxIterations) {
      this.iterationCount++;
      this.setOutputValue('iterations', this.iterationCount);
      return 'loopBody';
    } else {
      this.isLooping = false;
      return 'completed';
    }
  }

  /**
   * 继续循环执行
   */
  public continueLoop(): void {
    if (this.isLooping) {
      const condition = this.getInputValue('condition') === true;

      if (condition && this.iterationCount < this.maxIterations) {
        this.iterationCount++;
        this.setOutputValue('iterations', this.iterationCount);
        this.triggerFlow('loopBody');
      } else {
        this.isLooping = false;
        this.triggerFlow('completed');
      }
    }
  }

  /**
   * 重置循环状态
   */
  public resetLoop(): void {
    this.isLooping = false;
    this.iterationCount = 0;
  }
}

/**
 * Switch选择节点
 * 根据值选择执行路径
 */
export class SwitchNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加选择值输入
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '选择值',
      defaultValue: 0
    });

    // 添加多个case输出流程插槽
    for (let i = 0; i < 5; i++) {
      this.addInput({
        name: `case${i}`,
        type: SocketType.DATA,
        direction: SocketDirection.INPUT,
        dataType: 'any',
        description: `Case ${i} 值`,
        optional: true
      });

      this.addOutput({
        name: `case${i}`,
        type: SocketType.FLOW,
        direction: SocketDirection.OUTPUT,
        description: `Case ${i} 执行`
      });
    }

    // 添加默认输出流程插槽
    this.addOutput({
      name: 'default',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '默认执行'
    });
  }

  /**
   * 处理输入并确定输出流程
   * @param inputs 输入值
   * @returns 输出流程名称
   */
  protected process(inputs: Record<string, any>): string | null {
    const value = inputs.value;

    // 检查每个case
    for (let i = 0; i < 5; i++) {
      const caseValue = inputs[`case${i}`];
      if (caseValue !== undefined && value === caseValue) {
        return `case${i}`;
      }
    }

    // 没有匹配的case，执行默认分支
    return 'default';
  }
}

/**
 * 变量设置节点
 * 设置变量值
 */
export class SetVariableNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加变量名输入
    this.addInput({
      name: 'variableName',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '变量名',
      defaultValue: 'myVariable'
    });

    // 添加变量值输入
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '变量值',
      defaultValue: null
    });

    // 添加作用域输入
    this.addInput({
      name: 'scope',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '作用域 (local/global)',
      defaultValue: 'local'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加设置的值输出
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'any',
      description: '设置的值'
    });
  }

  /**
   * 处理输入并确定输出流程
   * @param inputs 输入值
   * @returns 输出流程名称
   */
  protected process(inputs: Record<string, any>): string | null {
    const variableName = inputs.variableName || 'myVariable';
    const value = inputs.value;
    const scope = inputs.scope || 'local';

    try {
      // 设置变量到执行上下文
      this.context.setVariable(variableName, value);

      // 设置输出值
      this.setOutputValue('value', value);

      return 'flow';
    } catch (error) {
      console.error(`设置变量失败: ${error}`);
      return 'flow';
    }
  }
}

/**
 * 变量获取节点
 * 获取变量值
 */
export class GetVariableNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加变量名输入
    this.addInput({
      name: 'variableName',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '变量名',
      defaultValue: 'myVariable'
    });

    // 添加默认值输入
    this.addInput({
      name: 'defaultValue',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '默认值',
      defaultValue: null
    });

    // 添加作用域输入
    this.addInput({
      name: 'scope',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '作用域 (local/global)',
      defaultValue: 'local'
    });

    // 添加变量值输出
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'any',
      description: '变量值'
    });

    // 添加是否存在输出
    this.addOutput({
      name: 'exists',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '变量是否存在'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const variableName = this.getInputValue('variableName') || 'myVariable';
    const defaultValue = this.getInputValue('defaultValue');
    const scope = this.getInputValue('scope') || 'local';

    try {
      let value: any;
      let exists: boolean;

      // 从执行上下文获取变量
      try {
        value = this.context.getVariable(variableName);
        exists = true;
      } catch {
        value = defaultValue;
        exists = false;
      }

      // 设置输出值
      this.setOutputValue('value', value);
      this.setOutputValue('exists', exists);

      return { value, exists };
    } catch (error) {
      console.error(`获取变量失败: ${error}`);
      this.setOutputValue('value', defaultValue);
      this.setOutputValue('exists', false);
      return { value: defaultValue, exists: false };
    }
  }
}

/**
 * Try-Catch异常处理节点
 * 处理执行过程中的异常
 */
export class TryCatchNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加Try输出流程插槽
    this.addOutput({
      name: 'try',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: 'Try块执行'
    });

    // 添加Catch输出流程插槽
    this.addOutput({
      name: 'catch',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: 'Catch块执行'
    });

    // 添加Finally输出流程插槽
    this.addOutput({
      name: 'finally',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: 'Finally块执行'
    });

    // 添加错误信息输出
    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '错误信息'
    });
  }

  /**
   * 处理输入并确定输出流程
   * @param inputs 输入值
   * @returns 输出流程名称
   */
  protected process(inputs: Record<string, any>): string | null {
    try {
      // 首先执行try块
      this.triggerFlow('try');
      return null;
    } catch (error) {
      // 如果发生错误，设置错误信息并执行catch块
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.setOutputValue('error', errorMessage);
      this.triggerFlow('catch');
      return null;
    } finally {
      // 最后执行finally块
      this.triggerFlow('finally');
    }
  }
}

/**
 * 数据类型转换节点
 * 转换数据类型
 */
export class TypeConvertNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入值
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '输入值',
      defaultValue: null
    });

    // 添加目标类型输入
    this.addInput({
      name: 'targetType',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '目标类型 (string/number/boolean/array/object)',
      defaultValue: 'string'
    });

    // 添加转换后的值输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'any',
      description: '转换后的值'
    });

    // 添加是否成功输出
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '转换是否成功'
    });

    // 添加原始类型输出
    this.addOutput({
      name: 'originalType',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '原始类型'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const value = this.getInputValue('value');
    const targetType = this.getInputValue('targetType') || 'string';
    const originalType = typeof value;

    try {
      let result: any;
      let success = true;

      switch (targetType.toLowerCase()) {
        case 'string':
          result = String(value);
          break;
        case 'number':
          result = Number(value);
          success = !isNaN(result);
          break;
        case 'boolean':
          result = Boolean(value);
          break;
        case 'array':
          if (Array.isArray(value)) {
            result = value;
          } else if (typeof value === 'string') {
            try {
              result = JSON.parse(value);
              success = Array.isArray(result);
            } catch {
              result = [value];
            }
          } else {
            result = [value];
          }
          break;
        case 'object':
          if (typeof value === 'object' && value !== null) {
            result = value;
          } else if (typeof value === 'string') {
            try {
              result = JSON.parse(value);
              success = typeof result === 'object';
            } catch {
              result = { value };
            }
          } else {
            result = { value };
          }
          break;
        default:
          result = value;
          success = false;
      }

      // 设置输出值
      this.setOutputValue('result', result);
      this.setOutputValue('success', success);
      this.setOutputValue('originalType', originalType);

      return { result, success, originalType };
    } catch (error) {
      console.error(`类型转换失败: ${error}`);
      this.setOutputValue('result', value);
      this.setOutputValue('success', false);
      this.setOutputValue('originalType', originalType);
      return { result: value, success: false, originalType };
    }
  }
}

/**
 * 数组操作节点
 * 基础数组操作
 */
export class ArrayOperationNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加数组输入
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'array',
      description: '输入数组',
      defaultValue: []
    });

    // 添加操作类型输入
    this.addInput({
      name: 'operation',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '操作类型 (length/push/pop/shift/unshift/slice/indexOf)',
      defaultValue: 'length'
    });

    // 添加值输入（用于push/unshift等操作）
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '操作值',
      optional: true
    });

    // 添加索引输入（用于slice等操作）
    this.addInput({
      name: 'startIndex',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '起始索引',
      defaultValue: 0,
      optional: true
    });

    this.addInput({
      name: 'endIndex',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '结束索引',
      optional: true
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'any',
      description: '操作结果'
    });

    // 添加修改后的数组输出
    this.addOutput({
      name: 'modifiedArray',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'array',
      description: '修改后的数组'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const inputArray = this.getInputValue('array') || [];
    const operation = this.getInputValue('operation') || 'length';
    const value = this.getInputValue('value');
    const startIndex = this.getInputValue('startIndex') || 0;
    const endIndex = this.getInputValue('endIndex');

    // 创建数组副本以避免修改原数组
    const array = Array.isArray(inputArray) ? [...inputArray] : [];
    let result: any;

    try {
      switch (operation.toLowerCase()) {
        case 'length':
          result = array.length;
          break;
        case 'push':
          result = array.push(value);
          break;
        case 'pop':
          result = array.pop();
          break;
        case 'shift':
          result = array.shift();
          break;
        case 'unshift':
          result = array.unshift(value);
          break;
        case 'slice':
          result = endIndex !== undefined ? array.slice(startIndex, endIndex) : array.slice(startIndex);
          break;
        case 'indexof':
          result = array.indexOf(value);
          break;
        case 'includes':
          result = array.includes(value);
          break;
        case 'join':
          result = array.join(typeof value === 'string' ? value : ',');
          break;
        case 'reverse':
          result = array.reverse();
          break;
        case 'sort':
          result = array.sort();
          break;
        default:
          result = array;
      }

      // 设置输出值
      this.setOutputValue('result', result);
      this.setOutputValue('modifiedArray', array);

      return { result, modifiedArray: array };
    } catch (error) {
      console.error(`数组操作失败: ${error}`);
      this.setOutputValue('result', null);
      this.setOutputValue('modifiedArray', inputArray);
      return { result: null, modifiedArray: inputArray };
    }
  }
}

/**
 * 注册核心节点
 * @param registry 节点注册表
 */
export function registerCoreNodes(registry: NodeRegistry): void {
  // 注册开始事件节点
  registry.registerNodeType({
    type: 'core/events/onStart',
    category: NodeCategory.EVENT,
    constructor: OnStartNode,
    label: '开始',
    description: '当视觉脚本开始执行时触发',
    icon: 'play',
    color: '#4CAF50',
    tags: ['event', 'core', 'lifecycle']
  });

  // 注册更新事件节点
  registry.registerNodeType({
    type: 'core/events/onUpdate',
    category: NodeCategory.EVENT,
    constructor: OnUpdateNode,
    label: '更新',
    description: '每帧更新时触发',
    icon: 'update',
    color: '#2196F3',
    tags: ['event', 'core', 'lifecycle']
  });

  // 注册分支节点
  registry.registerNodeType({
    type: 'core/flow/branch',
    category: NodeCategory.FLOW,
    constructor: BranchNode,
    label: '分支',
    description: '根据条件选择执行路径',
    icon: 'branch',
    color: '#FF9800',
    tags: ['flow', 'core', 'control']
  });

  // 注册序列节点
  registry.registerNodeType({
    type: 'core/flow/sequence',
    category: NodeCategory.FLOW,
    constructor: SequenceNode,
    label: '序列',
    description: '按顺序执行多个流程',
    icon: 'sequence',
    color: '#9C27B0',
    tags: ['flow', 'core', 'control']
  });

  // 注册For循环节点
  registry.registerNodeType({
    type: 'core/flow/forLoop',
    category: NodeCategory.FLOW,
    constructor: ForLoopNode,
    label: 'For循环',
    description: '执行指定次数的循环',
    icon: 'loop',
    color: '#FF5722',
    tags: ['flow', 'core', 'loop']
  });

  // 注册While循环节点
  registry.registerNodeType({
    type: 'core/flow/whileLoop',
    category: NodeCategory.FLOW,
    constructor: WhileLoopNode,
    label: 'While循环',
    description: '根据条件执行循环',
    icon: 'loop',
    color: '#E91E63',
    tags: ['flow', 'core', 'loop']
  });

  // 注册Switch选择节点
  registry.registerNodeType({
    type: 'core/flow/switch',
    category: NodeCategory.FLOW,
    constructor: SwitchNode,
    label: 'Switch选择',
    description: '根据值选择执行路径',
    icon: 'switch',
    color: '#673AB7',
    tags: ['flow', 'core', 'control']
  });

  // 注册变量设置节点
  registry.registerNodeType({
    type: 'core/variable/set',
    category: NodeCategory.VARIABLE,
    constructor: SetVariableNode,
    label: '设置变量',
    description: '设置变量值',
    icon: 'variable',
    color: '#009688',
    tags: ['variable', 'core', 'data']
  });

  // 注册变量获取节点
  registry.registerNodeType({
    type: 'core/variable/get',
    category: NodeCategory.VARIABLE,
    constructor: GetVariableNode,
    label: '获取变量',
    description: '获取变量值',
    icon: 'variable',
    color: '#4CAF50',
    tags: ['variable', 'core', 'data']
  });

  // 注册Try-Catch节点
  registry.registerNodeType({
    type: 'core/flow/tryCatch',
    category: NodeCategory.FLOW,
    constructor: TryCatchNode,
    label: '异常处理',
    description: '处理执行过程中的异常',
    icon: 'error',
    color: '#F44336',
    tags: ['flow', 'core', 'error']
  });

  // 注册类型转换节点
  registry.registerNodeType({
    type: 'core/data/typeConvert',
    category: NodeCategory.FUNCTION,
    constructor: TypeConvertNode,
    label: '类型转换',
    description: '转换数据类型',
    icon: 'convert',
    color: '#795548',
    tags: ['data', 'core', 'utility']
  });

  // 注册数组操作节点
  registry.registerNodeType({
    type: 'core/array/operation',
    category: NodeCategory.ARRAY,
    constructor: ArrayOperationNode,
    label: '数组操作',
    description: '基础数组操作',
    icon: 'array',
    color: '#607D8B',
    tags: ['array', 'core', 'data']
  });

  // 注册打印日志节点
  registry.registerNodeType({
    type: 'core/debug/print',
    category: NodeCategory.DEBUG,
    constructor: PrintLogNode,
    label: '打印日志',
    description: '在控制台打印日志',
    icon: 'print',
    color: '#F44336',
    tags: ['debug', 'core', 'utility']
  });

  // 注册延时节点
  registry.registerNodeType({
    type: 'core/flow/delay',
    category: NodeCategory.FLOW,
    constructor: DelayNode,
    label: '延时',
    description: '延时执行流程',
    icon: 'delay',
    color: '#00BCD4',
    tags: ['flow', 'core', 'time']
  });
}
