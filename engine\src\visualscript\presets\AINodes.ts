/**
 * 视觉脚本AI节点
 * 提供AI动画合成和自然语言处理相关的节点
 */
import type { Entity } from '../../core/Entity';
import { AsyncNode } from '../nodes/AsyncNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { FlowNode } from '../nodes/FlowNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { AIAnimationSynthesisSystem } from '../../animation/AIAnimationSynthesisSystem';
import { IntelligentDecisionSystem } from '../../ai/behavior/IntelligentDecisionSystem';
import { AIModelManager } from '../../ai/AIModelManager';
import { AIModelType } from '../../ai/AIModelType';

// 导入其他AI节点模块
import { registerAIModelNodes } from './AIModelNodes';
import { registerAIEmotionNodes } from './AIEmotionNodes';
import { registerAINLPNodes } from './AINLPNodes';

/**
 * 生成身体动画节点
 * 使用AI生成身体动画
 */
export class GenerateBodyAnimationNode extends AsyncNode {
  /** 请求ID */
  private requestId: string | null = null;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'prompt',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '提示文本',
      defaultValue: '走路'
    });

    this.addInput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '持续时间（秒）',
      defaultValue: 5.0
    });

    this.addInput({
      name: 'loop',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否循环',
      defaultValue: false
    });

    this.addInput({
      name: 'style',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '动画风格',
      defaultValue: 'natural',
      optional: true
    });

    this.addInput({
      name: 'intensity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '动画强度',
      defaultValue: 1.0,
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '生成成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '生成失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'animationClip',
      type: SocketType.DATA,
      dataType: 'AnimationClip',
      direction: SocketDirection.OUTPUT,
      description: '生成的动画片段'
    });

    this.addOutput({
      name: 'generationTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '生成时间（毫秒）'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const prompt = this.getInputValue('prompt') as string;
    const duration = this.getInputValue('duration') as number;
    const loop = this.getInputValue('loop') as boolean;
    const style = this.getInputValue('style') as string;
    const intensity = this.getInputValue('intensity') as number;

    // 检查输入值是否有效
    if (!entity || !prompt) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取AI动画合成系统
    const aiAnimationSystem = this.context.world.getSystem(AIAnimationSynthesisSystem);
    if (!aiAnimationSystem) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 生成身体动画
      this.requestId = aiAnimationSystem.generateBodyAnimation(entity, prompt, duration, {
        loop,
        style,
        intensity
      });

      if (!this.requestId) {
        this.triggerFlow('fail');
        return false;
      }

      // 获取AI动画合成组件
      const component = aiAnimationSystem.getAIAnimationSynthesis(entity);
      if (!component) {
        this.triggerFlow('fail');
        return false;
      }

      // 等待动画生成完成
      const result = await this.waitForAnimationResult(component, this.requestId);

      if (result && result.success && result.clip) {
        // 设置输出值
        this.setOutputValue('animationClip', result.clip);
        this.setOutputValue('generationTime', result.generationTime);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }

  /**
   * 等待动画生成结果
   * @param component AI动画合成组件
   * @param requestId 请求ID
   * @returns 生成结果
   */
  private async waitForAnimationResult(component: any, requestId: string): Promise<any> {
    const maxWaitTime = 30000; // 最大等待时间30秒
    const pollInterval = 100; // 轮询间隔100毫秒
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      const result = component.getResult(requestId);
      if (result) {
        return result;
      }

      // 等待一段时间后再次检查
      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }

    // 超时返回null
    return null;
  }

  /**
   * 取消执行
   */
  public cancel(): void {
    if (this.requestId) {
      // 获取AI动画合成系统
      const aiAnimationSystem = this.context.world.getSystem(AIAnimationSynthesisSystem);
      if (aiAnimationSystem) {
        // 取消请求
        const component = aiAnimationSystem.getAIAnimationSynthesis(this.getInputValue('entity') as Entity);
        if (component) {
          component.cancelRequest(this.requestId);
        }
      }

      this.requestId = null;
    }
  }
}

/**
 * 生成面部动画节点
 * 使用AI生成面部动画
 */
export class GenerateFacialAnimationNode extends AsyncNode {
  /** 请求ID */
  private requestId: string | null = null;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'prompt',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '提示文本',
      defaultValue: '你好，世界！'
    });

    this.addInput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '持续时间（秒）',
      defaultValue: 3.0
    });

    this.addInput({
      name: 'loop',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否循环',
      defaultValue: false
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '生成成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '生成失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'facialAnimationClip',
      type: SocketType.DATA,
      dataType: 'FacialAnimationClip',
      direction: SocketDirection.OUTPUT,
      description: '生成的面部动画片段'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const prompt = this.getInputValue('prompt') as string;
    const duration = this.getInputValue('duration') as number;
    const loop = this.getInputValue('loop') as boolean;

    // 检查输入值是否有效
    if (!entity || !prompt) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取AI动画合成系统
    const aiAnimationSystem = this.context.world.getSystem(AIAnimationSynthesisSystem);
    if (!aiAnimationSystem) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 生成面部动画
      this.requestId = aiAnimationSystem.generateFacialAnimation(entity, prompt, duration, {
        loop
      });

      if (!this.requestId) {
        this.triggerFlow('fail');
        return false;
      }

      // 获取AI动画合成组件
      const component = aiAnimationSystem.getAIAnimationSynthesis(entity);
      if (!component) {
        this.triggerFlow('fail');
        return false;
      }

      // 等待动画生成完成
      const result = await this.waitForAnimationResult(component, this.requestId);

      if (result && result.success && result.clip) {
        // 设置输出值
        this.setOutputValue('facialAnimationClip', result.clip);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }

  /**
   * 等待动画生成结果
   * @param component AI动画合成组件
   * @param requestId 请求ID
   * @returns 生成结果
   */
  private async waitForAnimationResult(component: any, requestId: string): Promise<any> {
    const maxWaitTime = 30000; // 最大等待时间30秒
    const pollInterval = 100; // 轮询间隔100毫秒
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      const result = component.getResult(requestId);
      if (result) {
        return result;
      }

      // 等待一段时间后再次检查
      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }

    // 超时返回null
    return null;
  }

  /**
   * 取消执行
   */
  public cancel(): void {
    if (this.requestId) {
      // 获取AI动画合成系统
      const aiAnimationSystem = this.context.world.getSystem(AIAnimationSynthesisSystem);
      if (aiAnimationSystem) {
        // 取消请求
        const component = aiAnimationSystem.getAIAnimationSynthesis(this.getInputValue('entity') as Entity);
        if (component) {
          component.cancelRequest(this.requestId);
        }
      }

      this.requestId = null;
    }
  }
}

/**
 * 生成组合动画节点
 * 使用AI生成身体和面部的组合动画
 */
export class GenerateCombinedAnimationNode extends AsyncNode {
  /** 请求ID */
  private requestId: string | null = null;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'prompt',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '提示文本',
      defaultValue: '开心地走路'
    });

    this.addInput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '持续时间（秒）',
      defaultValue: 5.0
    });

    this.addInput({
      name: 'loop',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否循环',
      defaultValue: false
    });

    this.addInput({
      name: 'style',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '动画风格',
      defaultValue: 'natural',
      optional: true
    });

    this.addInput({
      name: 'intensity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '动画强度',
      defaultValue: 1.0,
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '生成成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '生成失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'combinedAnimationClip',
      type: SocketType.DATA,
      dataType: 'CombinedAnimationClip',
      direction: SocketDirection.OUTPUT,
      description: '生成的组合动画片段'
    });

    this.addOutput({
      name: 'generationTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '生成时间（毫秒）'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const prompt = this.getInputValue('prompt') as string;
    const duration = this.getInputValue('duration') as number;
    const loop = this.getInputValue('loop') as boolean;
    const style = this.getInputValue('style') as string;
    const intensity = this.getInputValue('intensity') as number;

    // 检查输入值是否有效
    if (!entity || !prompt) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取AI动画合成系统
    const aiAnimationSystem = this.context.world.getSystem(AIAnimationSynthesisSystem);
    if (!aiAnimationSystem) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 生成组合动画
      this.requestId = aiAnimationSystem.generateCombinedAnimation(entity, prompt, duration, {
        loop,
        style,
        intensity
      });

      if (!this.requestId) {
        this.triggerFlow('fail');
        return false;
      }

      // 获取AI动画合成组件
      const component = aiAnimationSystem.getAIAnimationSynthesis(entity);
      if (!component) {
        this.triggerFlow('fail');
        return false;
      }

      // 等待动画生成完成
      const result = await this.waitForAnimationResult(component, this.requestId);

      if (result && result.success && result.clip) {
        // 设置输出值
        this.setOutputValue('combinedAnimationClip', result.clip);
        this.setOutputValue('generationTime', result.generationTime);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('生成组合动画失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }

  /**
   * 等待动画生成结果
   * @param component AI动画合成组件
   * @param requestId 请求ID
   * @returns 生成结果
   */
  private async waitForAnimationResult(component: any, requestId: string): Promise<any> {
    const maxWaitTime = 30000; // 最大等待时间30秒
    const pollInterval = 100; // 轮询间隔100毫秒
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      const result = component.getResult(requestId);
      if (result) {
        return result;
      }

      // 等待一段时间后再次检查
      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }

    // 超时返回null
    return null;
  }

  /**
   * 取消执行
   */
  public cancel(): void {
    if (this.requestId) {
      // 获取AI动画合成系统
      const aiAnimationSystem = this.context.world.getSystem(AIAnimationSynthesisSystem);
      if (aiAnimationSystem) {
        // 取消请求
        const component = aiAnimationSystem.getAIAnimationSynthesis(this.getInputValue('entity') as Entity);
        if (component) {
          component.cancelRequest(this.requestId);
        }
      }

      this.requestId = null;
    }
  }
}

/**
 * AI决策系统节点
 * 使用智能决策系统进行决策
 */
export class AIDecisionNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'context',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '决策上下文',
      defaultValue: {}
    });

    this.addInput({
      name: 'options',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '决策选项',
      defaultValue: []
    });

    this.addInput({
      name: 'strategy',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '决策策略',
      defaultValue: 'utility',
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '决策成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '决策失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'selectedOption',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '选择的选项'
    });

    this.addOutput({
      name: 'reasoning',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '决策理由'
    });

    this.addOutput({
      name: 'confidence',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '决策置信度'
    });

    this.addOutput({
      name: 'alternatives',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '备选方案'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const context = this.getInputValue('context') as any;
    const options = this.getInputValue('options') as any[];
    const strategy = this.getInputValue('strategy') as string;

    // 检查输入值是否有效
    if (!context || !Array.isArray(options) || options.length === 0) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 创建一个简单的黑板实例
      const blackboard = new Map<string, any>();

      // 创建智能决策系统实例（临时解决方案）
      // 在实际项目中，应该从世界中获取已注册的决策系统
      const decisionSystem = new IntelligentDecisionSystem(blackboard as any);

      // 进行决策
      const result = decisionSystem.makeDecision(context, options, strategy);

      if (result) {
        // 设置输出值
        this.setOutputValue('selectedOption', result.selectedOption);
        this.setOutputValue('reasoning', result.reasoning);
        this.setOutputValue('confidence', result.confidence);
        this.setOutputValue('alternatives', result.alternatives);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('AI决策失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * AI行为控制节点
 * 控制AI实体的智能行为
 */
export class AIBehaviorControlNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'behaviorType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '行为类型',
      defaultValue: 'idle'
    });

    this.addInput({
      name: 'parameters',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '行为参数',
      defaultValue: {},
      optional: true
    });

    this.addInput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '持续时间（秒）',
      defaultValue: -1,
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });

    this.addOutput({
      name: 'behaviorId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '行为ID'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const behaviorType = this.getInputValue('behaviorType') as string;
    const parameters = this.getInputValue('parameters') as any;
    const duration = this.getInputValue('duration') as number;

    // 检查输入值是否有效
    if (!entity || !behaviorType) {
      this.setOutputValue('success', false);
      this.setOutputValue('behaviorId', '');
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 这里应该调用AI行为系统来控制实体行为
      // 由于具体的行为系统可能还未实现，这里使用模拟逻辑
      const behaviorId = `behavior_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // 模拟行为控制，考虑持续时间
      console.log(`为实体 ${entity.id} 设置行为: ${behaviorType}`, {
        ...parameters,
        duration: duration > 0 ? duration : '无限制'
      });

      // 设置输出值
      this.setOutputValue('success', true);
      this.setOutputValue('behaviorId', behaviorId);

      // 触发输出流程
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('AI行为控制失败:', error);
      this.setOutputValue('success', false);
      this.setOutputValue('behaviorId', '');
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * AI路径规划节点
 * 使用AI进行智能路径规划
 */
export class AIPathPlanningNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'startPosition',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '起始位置'
    });

    this.addInput({
      name: 'endPosition',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '目标位置'
    });

    this.addInput({
      name: 'obstacles',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '障碍物列表',
      defaultValue: [],
      optional: true
    });

    this.addInput({
      name: 'algorithm',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '路径规划算法',
      defaultValue: 'astar',
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '规划成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '规划失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'path',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '规划的路径'
    });

    this.addOutput({
      name: 'pathLength',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '路径长度'
    });

    this.addOutput({
      name: 'planningTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '规划时间（毫秒）'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const startPosition = this.getInputValue('startPosition') as any;
    const endPosition = this.getInputValue('endPosition') as any;
    const obstacles = this.getInputValue('obstacles') as any[];
    const algorithm = this.getInputValue('algorithm') as string;

    // 检查输入值是否有效
    if (!entity || !startPosition || !endPosition) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      const startTime = Date.now();

      // 模拟AI路径规划
      // 在实际项目中，这里应该调用真正的路径规划算法
      const path = this.simulatePathPlanning(startPosition, endPosition, obstacles, algorithm);

      const endTime = Date.now();
      const planningTime = endTime - startTime;

      if (path && path.length > 0) {
        // 计算路径长度
        const pathLength = this.calculatePathLength(path);

        // 设置输出值
        this.setOutputValue('path', path);
        this.setOutputValue('pathLength', pathLength);
        this.setOutputValue('planningTime', planningTime);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('AI路径规划失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }

  /**
   * 模拟路径规划
   * @param start 起始位置
   * @param end 目标位置
   * @param obstacles 障碍物
   * @param algorithm 算法
   * @returns 路径
   */
  private simulatePathPlanning(start: any, end: any, obstacles: any[], algorithm: string): any[] {
    // 简单的直线路径模拟
    const path = [start];

    // 根据算法类型调整路径规划策略
    if (algorithm === 'astar' || algorithm === 'dijkstra') {
      // 对于复杂算法，考虑障碍物
      if (obstacles && obstacles.length > 0) {
        // 简单的避障逻辑：添加绕行点
        const avoidancePoint = {
          x: start.x + (end.x - start.x) * 0.3,
          y: start.y + (end.y - start.y) * 0.3 + 1, // 稍微偏移避开障碍物
          z: start.z + (end.z - start.z) * 0.3
        };
        path.push(avoidancePoint);
      }
    }

    // 添加中间点
    const midPoint = {
      x: (start.x + end.x) / 2,
      y: (start.y + end.y) / 2,
      z: (start.z + end.z) / 2
    };

    path.push(midPoint);
    path.push(end);

    return path;
  }

  /**
   * 计算路径长度
   * @param path 路径
   * @returns 长度
   */
  private calculatePathLength(path: any[]): number {
    let length = 0;
    for (let i = 1; i < path.length; i++) {
      const prev = path[i - 1];
      const curr = path[i];
      const dx = curr.x - prev.x;
      const dy = curr.y - prev.y;
      const dz = curr.z - prev.z;
      length += Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
    return length;
  }
}

/**
 * AI模型管理节点
 * 管理AI模型的加载、卸载和切换
 */
export class AIModelManagementNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'action',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '操作类型',
      defaultValue: 'load'
    });

    this.addInput({
      name: 'modelType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '模型类型',
      defaultValue: 'gpt'
    });

    this.addInput({
      name: 'modelConfig',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '模型配置',
      defaultValue: {},
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '操作成功'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '操作失败'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'modelId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '模型ID'
    });

    this.addOutput({
      name: 'modelInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '模型信息'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public async execute(): Promise<any> {
    // 获取输入值
    const action = this.getInputValue('action') as string;
    const modelType = this.getInputValue('modelType') as string;
    const modelConfig = this.getInputValue('modelConfig') as any;

    // 检查输入值是否有效
    if (!action || !modelType) {
      this.triggerFlow('fail');
      return false;
    }

    // 获取AI模型管理器
    const aiModelManager = this.context.world.getSystem(AIModelManager);
    if (!aiModelManager) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      let result: any = null;
      let modelId = '';
      let modelInfo: any = {};

      switch (action) {
        case 'load':
          // 将字符串转换为AIModelType枚举
          const modelTypeEnum = modelType as AIModelType;
          result = await aiModelManager.loadModel(modelTypeEnum, modelConfig);
          if (result) {
            modelId = (result as any).getId ? (result as any).getId() : modelType;
            modelInfo = {
              type: modelType,
              config: modelConfig
            };
          }
          break;

        case 'unload':
          result = aiModelManager.unloadModel(modelType);
          modelId = modelType;
          break;

        case 'list':
          result = aiModelManager.getAllModels();
          modelInfo = Array.from(result.keys());
          break;

        case 'info':
          const model = aiModelManager.getModel(modelType);
          if (model) {
            result = model;
            modelId = modelType;
            modelInfo = {
              type: modelType,
              config: modelConfig
            };
          }
          break;

        default:
          throw new Error(`不支持的操作类型: ${action}`);
      }

      if (result !== null) {
        // 设置输出值
        this.setOutputValue('modelId', modelId);
        this.setOutputValue('modelInfo', modelInfo);

        // 触发成功流程
        this.triggerFlow('success');
        return true;
      } else {
        // 触发失败流程
        this.triggerFlow('fail');
        return false;
      }
    } catch (error) {
      console.error('AI模型管理失败:', error);
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * AI性能优化节点
 * 优化AI系统的性能
 */
export class AIPerformanceOptimizationNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入数据插槽
    this.addInput({
      name: 'optimizationType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '优化类型',
      defaultValue: 'memory'
    });

    this.addInput({
      name: 'targetSystem',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '目标系统',
      defaultValue: 'all',
      optional: true
    });

    this.addInput({
      name: 'optimizationLevel',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '优化级别',
      defaultValue: 1,
      optional: true
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });

    this.addOutput({
      name: 'optimizationResult',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '优化结果'
    });

    this.addOutput({
      name: 'performanceGain',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '性能提升百分比'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const optimizationType = this.getInputValue('optimizationType') as string;
    const targetSystem = this.getInputValue('targetSystem') as string;
    const optimizationLevel = this.getInputValue('optimizationLevel') as number;

    try {
      // 模拟性能优化
      const result = this.performOptimization(optimizationType, targetSystem, optimizationLevel);

      // 设置输出值
      this.setOutputValue('success', result.success);
      this.setOutputValue('optimizationResult', result);
      this.setOutputValue('performanceGain', result.performanceGain);

      return result.success;
    } catch (error) {
      console.error('AI性能优化失败:', error);
      this.setOutputValue('success', false);
      this.setOutputValue('optimizationResult', { error: error.message });
      this.setOutputValue('performanceGain', 0);
      return false;
    }
  }

  /**
   * 执行性能优化
   * @param type 优化类型
   * @param system 目标系统
   * @param level 优化级别
   * @returns 优化结果
   */
  private performOptimization(type: string, system: string, level: number): any {
    // 模拟优化逻辑
    const optimizations = {
      memory: () => ({ description: '内存优化', gain: 15 * level }),
      cpu: () => ({ description: 'CPU优化', gain: 20 * level }),
      gpu: () => ({ description: 'GPU优化', gain: 25 * level }),
      network: () => ({ description: '网络优化', gain: 10 * level }),
      cache: () => ({ description: '缓存优化', gain: 30 * level })
    };

    const optimization = optimizations[type as keyof typeof optimizations];
    if (!optimization) {
      throw new Error(`不支持的优化类型: ${type}`);
    }

    const optimizationData = optimization();

    return {
      success: true,
      type,
      system,
      level,
      description: optimizationData.description,
      performanceGain: Math.min(optimizationData.gain, 100), // 最大100%提升
      timestamp: Date.now(),
      details: {
        beforeOptimization: 100,
        afterOptimization: 100 + optimizationData.gain,
        optimizationSteps: [
          `分析${system}系统性能`,
          `应用${optimizationData.description}`,
          `验证优化效果`
        ]
      }
    };
  }
}

/**
 * 注册AI节点
 * @param registry 节点注册表
 */
export function registerAINodes(registry: NodeRegistry): void {
  // 注册生成身体动画节点
  registry.registerNodeType({
    type: 'ai/animation/generateBodyAnimation',
    category: NodeCategory.AI,
    constructor: GenerateBodyAnimationNode,
    label: '生成身体动画',
    description: '使用AI生成身体动画',
    icon: 'body',
    color: '#673AB7',
    tags: ['ai', 'animation', 'body']
  });

  // 注册生成面部动画节点
  registry.registerNodeType({
    type: 'ai/animation/generateFacialAnimation',
    category: NodeCategory.AI,
    constructor: GenerateFacialAnimationNode,
    label: '生成面部动画',
    description: '使用AI生成面部动画',
    icon: 'face',
    color: '#673AB7',
    tags: ['ai', 'animation', 'facial']
  });

  // 注册生成组合动画节点
  registry.registerNodeType({
    type: 'ai/animation/generateCombinedAnimation',
    category: NodeCategory.AI,
    constructor: GenerateCombinedAnimationNode,
    label: '生成组合动画',
    description: '使用AI生成身体和面部的组合动画',
    icon: 'combined',
    color: '#673AB7',
    tags: ['ai', 'animation', 'combined']
  });

  // 注册AI决策系统节点
  registry.registerNodeType({
    type: 'ai/behavior/decision',
    category: NodeCategory.AI,
    constructor: AIDecisionNode,
    label: 'AI决策系统',
    description: '使用智能决策系统进行决策',
    icon: 'decision',
    color: '#673AB7',
    tags: ['ai', 'decision', 'behavior']
  });

  // 注册AI行为控制节点
  registry.registerNodeType({
    type: 'ai/behavior/control',
    category: NodeCategory.AI,
    constructor: AIBehaviorControlNode,
    label: 'AI行为控制',
    description: '控制AI实体的智能行为',
    icon: 'behavior',
    color: '#673AB7',
    tags: ['ai', 'behavior', 'control']
  });

  // 注册AI路径规划节点
  registry.registerNodeType({
    type: 'ai/navigation/pathPlanning',
    category: NodeCategory.AI,
    constructor: AIPathPlanningNode,
    label: 'AI路径规划',
    description: '使用AI进行智能路径规划',
    icon: 'path',
    color: '#673AB7',
    tags: ['ai', 'navigation', 'path', 'planning']
  });

  // 注册AI模型管理节点
  registry.registerNodeType({
    type: 'ai/model/management',
    category: NodeCategory.AI,
    constructor: AIModelManagementNode,
    label: 'AI模型管理',
    description: '管理AI模型的加载、卸载和切换',
    icon: 'model',
    color: '#673AB7',
    tags: ['ai', 'model', 'management']
  });

  // 注册AI性能优化节点
  registry.registerNodeType({
    type: 'ai/performance/optimization',
    category: NodeCategory.AI,
    constructor: AIPerformanceOptimizationNode,
    label: 'AI性能优化',
    description: '优化AI系统的性能',
    icon: 'optimization',
    color: '#673AB7',
    tags: ['ai', 'performance', 'optimization']
  });

  // 注册AI模型节点
  registerAIModelNodes(registry);

  // 注册AI情感节点
  registerAIEmotionNodes(registry);

  // 注册AI自然语言处理节点
  registerAINLPNodes(registry);

  console.log('已注册所有AI节点类型');
}
