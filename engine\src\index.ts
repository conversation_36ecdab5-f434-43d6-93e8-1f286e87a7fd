/**
 * DL（Digital Learning）引擎入口文件
 * 导出所有公共API
 */

// 核心模块
export * from './core/Engine';
export * from './core/World';
export * from './core/Entity';
export * from './core/Component';
export * from './core/System';

// 渲染模块
export * from './rendering/Renderer';
export * from './rendering/Camera';
export * from './rendering/RenderSystem';
export * from './rendering/materials/index';
export * from './rendering/Light';

// 场景模块
export * from './scene/Scene';
export * from './scene/SceneManager';
export * from './scene/Transform';
export * from './scene/Skybox';

// 物理模块
export * from './physics/PhysicsSystem';
export * from './physics/PhysicsRaycastResult';
export * from './physics/character/CharacterController';
export * from './physics/ccd/ContinuousCollisionDetection';
export * from './physics/debug/PhysicsDebugger';
export * from './physics/debug/EnhancedPhysicsDebugger';
// 软体物理模块
export * from './physics/softbody/SoftBodySystem';
export * from './physics/softbody/SoftBodyComponent';
export * from './physics/constraints/SliderConstraint';
export * from './physics/constraints/FixedConstraint';
export * from './physics/constraints/WheelConstraint';

// 导出物理体相关，解决命名冲突
import type { PhysicsBody } from './physics/PhysicsBody';
import type { BodyType, PhysicsBodyOptions } from './physics/PhysicsBody';
export { PhysicsBody };
export type { BodyType, PhysicsBodyOptions };

// 导出物理碰撞体相关，解决命名冲突
import { PhysicsCollider } from './physics/PhysicsCollider';
import type { ColliderType as PhysicsColliderType, ColliderOptions as PhysicsColliderOptions } from './physics/PhysicsCollider';
export { PhysicsCollider };
export type { PhysicsColliderType, PhysicsColliderOptions };

// 导出物理组件，解决命名冲突
export * from './physics/components/CharacterControllerComponent';
export * from './physics/components/PhysicsConstraintComponent';
export * from './physics/components/PhysicsWorldComponent';

// 单独导出物理体组件和碰撞体组件，避免命名冲突
import { PhysicsBodyComponent } from './physics/components/PhysicsBodyComponent';
import { PhysicsColliderComponent } from './physics/components/PhysicsColliderComponent';
export { PhysicsBodyComponent, PhysicsColliderComponent };

// 粒子模块
export * from './particles/ParticleSystem';
export * from './particles/ParticleEmitter';
export * from './particles/Particle';

// 资产模块
export * from './assets/AssetManager';
export * from './assets/AssetLoader';

// 单独导出 ResourceManager 以避免命名冲突
import { ResourceManager } from './assets/ResourceManager';
import type { AssetType as ResourceAssetType, ResourceState, ResourceInfo, ResourceManagerOptions } from './assets/ResourceManager';
export { ResourceManager };
export type { ResourceAssetType, ResourceState, ResourceInfo, ResourceManagerOptions };

// GLTF模块
export * from './gltf';

// 数学模块
// 以下模块暂未实现，使用 three.js 的数学库代替
// export * from './math/Vector2';
// export * from './math/Vector3';
// export * from './math/Quaternion';
// export * from './math/Matrix4';

// 动画模块
import type { AnimationClip } from './animation/AnimationClip';
import { Animator } from './animation/Animator';
import { AnimationSystem } from './animation/AnimationSystem';
import { BlendSpace1D } from './animation/BlendSpace1D';
import { BlendSpace2D } from './animation/BlendSpace2D';
import { AnimationStateMachine } from './animation/AnimationStateMachine';

// 导出动画系统类
export {
  AnimationClip,
  Animator,
  AnimationSystem,
  BlendSpace1D,
  BlendSpace2D,
  AnimationStateMachine
};

// 重命名可能冲突的类型
import type { AnimationState, AnimationEventType } from './animation/Animator';
export type { AnimationState as AnimatorState, AnimationEventType as AnimatorEventType };

// UI模块
export * from './ui/UIModule';

// 输入模块 - 使用具名导出避免冲突
import { InputSystem } from './input/InputSystem';
import { InputManager } from './input/InputManager';
import { InputEventType as SystemInputEventType, MouseButton as SystemMouseButton } from './input/InputSystem';
import type { InputManagerOptions } from './input/InputManager';

export { InputSystem, InputManager };
export type { InputManagerOptions, SystemInputEventType, SystemMouseButton };

// 其他输入模块
export * from './input/InputDevice';
export * from './input/InputAction';
export * from './input/InputBinding';
export * from './input/InputMapping';
export * from './input/InputRecorder';
export * from './input/InputVisualizer';
export * from './input/components/InputComponent';
export * from './input/devices/KeyboardDevice';
export * from './input/devices/MouseDevice';
export * from './input/devices/GamepadDevice';
export * from './input/devices/TouchDevice';
export * from './input/devices/XRDevice';

// 交互模块 - 单独导出以避免命名冲突
import { InteractionSystem } from './interaction/InteractionSystem';
import type { InteractionSystemConfig } from './interaction/InteractionSystem';
import { InteractableComponent, InteractionType as InteractableInteractionType } from './interaction/components/InteractableComponent';
import type { InteractableComponentConfig, InteractionCallback } from './interaction/components/InteractableComponent';
import { InteractionEventComponent, InteractionEventType, InteractionEvent } from './interaction/components/InteractionEventComponent';
import type { InteractionEventComponentConfig, InteractionEventData, InteractionEventListener } from './interaction/components/InteractionEventComponent';
import { InteractionPromptComponent, PromptPositionType } from './interaction/components/InteractionPromptComponent';
import type { InteractionPromptComponentConfig } from './interaction/components/InteractionPromptComponent';
import { InteractionHighlightComponent, HighlightType } from './interaction/components/InteractionHighlightComponent';
import type { InteractionHighlightComponentConfig } from './interaction/components/InteractionHighlightComponent';
import { GrabSystem } from './interaction/systems/GrabSystem';
import type { GrabSystemConfig } from './interaction/systems/GrabSystem';
import { GrabbableComponent, GrabType, Hand } from './interaction/components/GrabbableComponent';
import type { GrabbableComponentConfig } from './interaction/components/GrabbableComponent';
import { GrabberComponent } from './interaction/components/GrabberComponent';
import type { GrabberComponentConfig } from './interaction/components/GrabberComponent';
import { GrabbedComponent } from './interaction/components/GrabbedComponent';
import type { GrabbedComponentConfig } from './interaction/components/GrabbedComponent';
import { PhysicsGrabComponent } from './interaction/components/PhysicsGrabComponent';
import type { PhysicsGrabComponentConfig } from './interaction/components/PhysicsGrabComponent';
import { GrabState, GrabEventType } from './interaction/state/GrabState';
import type { GrabEventData } from './interaction/state/GrabState';

export {
  InteractionSystem,
  InteractableComponent,
  InteractableInteractionType,
  InteractionEventComponent,
  InteractionEventType,
  InteractionEvent,
  InteractionPromptComponent,
  PromptPositionType,
  InteractionHighlightComponent,
  HighlightType,
  GrabSystem,
  GrabbableComponent,
  GrabType,
  Hand,
  GrabberComponent,
  GrabbedComponent,
  PhysicsGrabComponent,
  GrabState,
  GrabEventType
};

export type {
  InteractionSystemConfig,
  InteractableComponentConfig,
  InteractionCallback,
  InteractionEventComponentConfig,
  InteractionEventData,
  InteractionEventListener,
  InteractionPromptComponentConfig,
  InteractionHighlightComponentConfig,
  GrabSystemConfig,
  GrabbableComponentConfig,
  GrabberComponentConfig,
  GrabbedComponentConfig,
  PhysicsGrabComponentConfig,
  GrabEventData
};

// 音频模块
export * from './audio/AudioSystem';
export * from './audio/AudioSource';
export * from './audio/AudioListener';

// 网络模块
export * from './network/NetworkSystem';
export * from './network/NetworkManager';
export * from './network/NetworkConnection';
export * from './network/WebSocketConnection';
export * from './network/WebRTCConnection';
export * from './network/NetworkMessage';
export * from './network/NetworkEvent';
export * from './network/NetworkEntity';
export * from './network/NetworkUser';
export * from './network/MessageType';
export * from './network/MessageSerializer';
export * from './network/components/NetworkEntityComponent';
export * from './network/components/NetworkTransformComponent';
export * from './network/components/NetworkUserComponent';

// 工具模块
export * from './utils/EventEmitter';
export * from './utils/Time';
export * from './utils/UUID';

// 单独导出 Debug 和 Logger 以避免 LogLevel 冲突
import { Debug } from './utils/Debug';
import type { LogLevel as DebugLogLevel } from './utils/Debug';
import { Logger, LogLevel as LoggerLogLevel, defaultLogger, createLogger } from './utils/Logger';
import type { LoggerOptions } from './utils/Logger';

export { Debug, Logger, defaultLogger, createLogger };
export type { DebugLogLevel, LoggerLogLevel, LoggerOptions };

// 角色模块
export * from './avatar/controllers';

// 国际化模块
export * from './i18n/I18n';

// 视觉脚本模块 - 使用具名导出避免冲突
import { VisualScriptSystem } from './visualscript/VisualScriptSystem';

export { VisualScriptSystem };

// 其他视觉脚本模块 - 使用具名导出避免ValidationResult冲突
export * from './visualscript/nodes/Node';
export * from './visualscript/execution/ExecutionContext';
export * from './visualscript/execution/Fiber';

// 视觉脚本节点注册表 - 具名导出避免ValidationResult冲突
import {
  NodeRegistry,
  ValidationResult as NodeValidationResult,
  NodeConstructor,
  NodeFactory,
  NodeValidator,
  NodeTypeInfo,
  NodeStatistics
} from './visualscript/nodes/NodeRegistry';

export { NodeRegistry };
export type {
  NodeValidationResult,
  NodeConstructor,
  NodeFactory,
  NodeValidator,
  NodeTypeInfo,
  NodeStatistics
};

// 区块链模块 - 使用具名导出避免冲突
import { BlockchainManager } from './blockchain/core/BlockchainManager';
import { CacheConfig as BlockchainCacheConfig } from './blockchain/cache/BlockchainCache';

export { BlockchainManager };
export type { BlockchainCacheConfig };

// 其他区块链模块
export * from './blockchain/types/BlockchainTypes';
export * from './blockchain/types/NFTTypes';
export * from './blockchain/core/WalletManager';
export * from './blockchain/core/ContractManager';

// 导航模块 - 使用具名导出避免ValidationResult冲突
import {
  AvatarPath,
  PathPoint,
  PathInterpolator,
  PathValidator,
  ValidationResult as NavigationValidationResult,
  ValidationOptions as NavigationValidationOptions,
  AvatarPathComponent,
  PathFollowingComponent,
  PathFollowingState,
  AvatarPathSystem
} from './navigation';

export {
  AvatarPath,
  PathPoint,
  PathInterpolator,
  PathValidator,
  AvatarPathComponent,
  PathFollowingComponent,
  PathFollowingState,
  AvatarPathSystem
};

export type {
  NavigationValidationResult,
  NavigationValidationOptions
};

// 其他导航类型
export type {
  PathTrigger,
  PathMetadata,
  AvatarPathData,
  LoopMode,
  InterpolationType,
  PathEventType,
  AvatarPathOptions,
  PathFollowingOptions,
  NavigationSystemOptions,
  NavigationMeshOptions,
  PathFinderOptions,
  NavigationAgentOptions,
  NavigationObstacleOptions,
  PathEventData,
  NavigationPath,
  NavigationNode,
  NavigationTriangle,
  NavigationEdge
} from './navigation';

// AI模块
export * from './ai/AIContentGenerator';
export * from './ai/AIRecommendationEngine';
export * from './ai/AIEmotionAnalysisSystem';
export * from './ai/recommendation/ContentFeatureExtractor';
export * from './ai/recommendation/RealtimeRecommendationCache';
export * from './ai/recommendation/UserBehaviorAnalyzer';
